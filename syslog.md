
Jul 24 19:44:14 :: Loading rooms.
Jul 24 19:44:14 ::    50233 rooms, 11654056 bytes.
Jul 24 19:44:14 :: SYSERR: dg_read_trigger: Trigger vnum #2315 asked for but non-existant! (room:-1)
...
Jul 24 19:44:15 :: Loading mobs and generating index.
Jul 24 19:44:15 ::    14563 mobs, 466016 bytes in index, 260502944 bytes in prototypes.
Jul 24 19:44:16 :: SYSERR: dg_read_trigger: Trigger vnum #2314 asked for but non-existant! (mob: the Dark Knight - -1)
Jul 24 19:44:16 :: SYSERR: dg_read_trigger: Trigger vnum #2316 asked for but non-existant! (mob: the Dark Knight - -1)
Jul 24 19:44:16 :: SYSERR: dg_read_trigger: Trigger vnum #2310 asked for but non-existant! (mob: a giant mother spider - -1)
Jul 24 19:44:16 :: SYSERR: dg_read_trigger: Trigger vnum #2313 asked for but non-existant! (mob: a bat-like creature - -1)
Jul 24 19:44:41 :: Loading objs and generating index.
Jul 24 19:44:41 ::    12160 objs, 389120 bytes in index, 7587840 bytes in prototypes.
Jul 24 19:44:41 :: SYSERR: Trigger vnum #2308 asked for but non-existant! (Object: a jet black pearl - -1)
Jul 24 19:44:41 :: SYSERR: Trigger vnum #2311 asked for but non-existant! (Object: a large stone chest - -1)
Jul 24 19:44:41 :: SYSERR: Trigger vnum #2317 asked for but non-existant! (Object: Helm of Brilliance - -1)
Jul 24 19:44:41 :: Renumbering zone table.
Jul 24 19:44:41 :: SYSERR: zone file: Invalid vnum 15802, cmd disabled
Jul 24 19:44:41 :: SYSERR: ...offending cmd: 'O' cmd in zone #158, line 8
...
Jul 24 19:44:41 :: Loading evolutions.
Jul 24 19:44:41 :: Object (V) 19216 does not exist in database.
Jul 24 19:44:41 :: Object (V) 19216 does not exist in database.
...
Jul 24 19:44:41 :: Assigning function pointers:
Jul 24 19:44:41 ::    Mobiles.
Jul 24 19:44:41 ::    Shopkeepers.
Jul 24 19:44:41 ::    Objects.
Jul 24 19:44:41 ::    Rooms.
Jul 24 19:44:41 ::    Questmasters.
Jul 24 19:44:41 :: SYSERR: Quest #0 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #2011 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20309 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20315 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20316 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20317 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20318 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20319 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20320 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20321 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20322 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20323 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20324 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #20325 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #102412 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #102413 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #102414 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #102415 has no questmaster specified.
Jul 24 19:44:41 :: SYSERR: Quest #128102 has no questmaster specified.
...
Jul 24 19:44:41 :: Resetting #77: The Crystal Swamp (rooms 7700-7799).
Jul 24 19:44:41 :: SYSERR: Mob using '((ch)->player_specials->saved.pref)' at treasure.c:1525.
Jul 24 19:44:41 :: SYSERR: Mob using '((ch)->player_specials->saved.pref)' at treasure.c:1525.
...

Jul 24 19:52:47 :: SYSERR: 	YBrother Spire	n (#200103): Attempting to call non-existing mob function.
Jul 24 19:52:47 :: SYSERR: Jakur the tanner (#125913): Attempting to call non-existing mob function.
Jul 24 19:52:47 :: SYSERR: Adoril (#21605): Attempting to call non-existing mob function.
...
Jul 24 19:53:41 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1284.
Jul 24 19:53:41 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1287.
Jul 24 19:53:41 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1287.
Jul 24 19:53:41 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1290.
Jul 24 19:53:41 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1290.
Jul 24 19:53:41 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1291.
Jul 24 19:53:41 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1291.
Jul 24 19:53:41 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1292.
Jul 24 19:53:41 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1307.
Jul 24 19:53:41 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1307.
Jul 24 19:53:41 :: SYSERR: 1527 is lacking DAM_
Jul 24 19:54:34 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:54:40 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:54:41 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:54:45 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:54:48 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:54:50 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:54:55 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:54:57 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:54:57 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:54:59 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:00 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:03 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:03 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:05 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:05 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:07 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:08 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:09 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:11 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:13 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:15 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:15 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:15 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:16 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:18 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:27 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:33 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:35 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:35 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:35 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:37 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:39 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:41 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
...
Jul 24 19:55:43 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:45 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:47 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:47 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:49 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:53 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:55:53 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:03 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:05 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:05 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:05 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:07 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:09 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:13 :: SYSERR: NULL object (0x26867de0) or same source (0x26867de0) and target ((nil)) obj passed to obj_to_obj.
Jul 24 19:56:13 :: SYSERR: Couldn't find 1 extractions as counted.
Jul 24 19:56:23 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:29 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:33 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:35 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:35 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:39 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:41 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
...
Jul 24 19:56:47 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:49 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:49 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:55 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:57 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:56:59 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:01 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:05 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:07 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:07 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:09 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:11 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:11 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:11 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:11 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:17 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:17 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:19 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:25 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:25 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:27 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:31 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:33 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
...
Jul 24 19:57:47 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:57:49 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:01 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:05 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:05 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:07 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:09 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:09 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:13 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:13 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:17 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:17 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:21 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:23 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:23 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:58:27 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
...

Jul 24 19:59:39 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:59:45 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:59:45 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:59:45 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:59:47 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:59:53 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 19:59:57 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 20:00:03 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 20:00:17 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 20:00:23 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 20:00:29 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 20:00:31 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 20:00:33 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 20:00:35 :: SYSERR: NULL object (0x27276620) or same source (0x27276620) and target ((nil)) obj passed to obj_to_obj.
Jul 24 20:00:35 :: SYSERR: Couldn't find 1 extractions as counted.
Jul 24 20:00:37 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
Jul 24 20:00:47 :: SYSERR: Mob using '((vict)->player_specials->saved.pref)' at spec_procs.c:6315.
...
Jul 24 20:02:41 :: PERF: affect_update() - Total: 28046 chars (28045 NPCs, 1 PCs), Affected: 7084, Affects processed: 45375
Jul 24 20:02:42 :: nusage: 1   sockets connected, 1   sockets playing
Jul 24 20:07:29 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1250.
Jul 24 20:07:29 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1250.
Jul 24 20:07:29 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1265.
Jul 24 20:07:29 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1268.
Jul 24 20:07:29 :: SYSERR: Mob using '((ch)->player_specials->saved.psionic_energy_type)' at magic.c:1268.
Jul 24 20:07:29 :: SYSERR: 1507 is lacking DAM_
Jul 24 20:07:42 :: nusage: 1   sockets connected, 1   sockets playing
Jul 24 20:12:41 :: PERF: affect_update() - Total: 28795 chars (28794 NPCs, 1 PCs), Affected: 7321, Affects processed: 72941
Jul 24 20:12:42 :: nusage: 1   sockets connected, 1   sockets playing
